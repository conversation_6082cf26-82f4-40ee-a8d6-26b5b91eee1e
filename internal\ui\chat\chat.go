/**
 * @license
 * Copyright 2025 Arien LLC
 * License: MIT
 */

package chat

import (
	"context"
	"fmt"
	"strings"
	"time"

	"arien/internal/core"
	"arien/internal/ui/components"
	"arien/internal/ui/themes"

	"github.com/charmbracelet/bubbles/viewport"
	tea "github.com/charmbracelet/bubbletea"
	"github.com/charmbracelet/lipgloss"
	"github.com/charmbracelet/log"
)

// ChatApp handles the main chat interface
type ChatApp struct {
	engine *core.Engine
	logger *log.Logger
}

// chatModel represents the enhanced chat UI model
type chatModel struct {
	engine          *core.Engine
	logger          *log.Logger
	messages        []components.Message
	width           int
	height          int
	loading         bool
	err             error
	theme           themes.Theme

	// Components
	viewport        viewport.Model
	inputComponent  *components.InputComponent
	messageRenderer *components.MessageRenderer
	progressComponent *components.ProgressComponent

	// State
	showTools       bool
	showDiff        bool
	diffViewer      *components.DiffViewer

	// Layout
	layout          *components.LayoutComponent
}

// NewChatApp creates a new chat app
func NewChatApp(engine *core.Engine, logger *log.Logger) *ChatApp {
	return &ChatApp{
		engine: engine,
		logger: logger,
	}
}

// Run starts the enhanced chat interface
func (c *ChatApp) Run(ctx context.Context) error {
	// Get current theme from config
	currentTheme := themes.GetDefaultTheme()
	if themeName := c.engine.Config().GetTheme(); themeName != "" {
		if theme, exists := themes.GetTheme(themeName); exists {
			currentTheme = theme
		}
	}

	model := chatModel{
		engine:   c.engine,
		logger:   c.logger,
		messages: []components.Message{},
		width:    100,
		height:   30,
		theme:    currentTheme,
	}

	// Initialize components
	model.inputComponent = components.NewInputComponent(components.InputTypeText, currentTheme)
	model.inputComponent.SetPlaceholder("Type your message here...")
	model.inputComponent.SetFocus(true)

	model.messageRenderer = components.NewMessageRenderer(currentTheme, model.width-4)
	model.progressComponent = components.NewProgressComponent(components.ProgressTypeSpinner, currentTheme)

	model.viewport = viewport.New(model.width-4, model.height-8)

	model.layout = components.NewLayoutComponent(components.LayoutTypeBorder, currentTheme)
	model.layout.SetTitle("🤖 Arien - AI Assistant")

	// Add welcome message
	welcomeMsg := components.Message{
		ID:        "welcome",
		Type:      components.MessageTypeAssistant,
		Content:   "Hello! I'm Arien, your AI-powered software engineering assistant. I have access to 19+ built-in tools for development, debugging, testing, and automation. How can I help you today?",
		Timestamp: time.Now(),
		Author:    "Arien",
	}
	model.messages = append(model.messages, welcomeMsg)
	model.updateViewport()

	p := tea.NewProgram(model, tea.WithAltScreen())

	if _, err := p.Run(); err != nil {
		return fmt.Errorf("chat UI failed: %w", err)
	}

	return nil
}

// Init initializes the model
func (m chatModel) Init() tea.Cmd {
	return nil
}

// Update handles messages with enhanced functionality
func (m chatModel) Update(msg tea.Msg) (tea.Model, tea.Cmd) {
	var cmd tea.Cmd
	var cmds []tea.Cmd

	switch msg := msg.(type) {
	case tea.WindowSizeMsg:
		m.width = msg.Width
		m.height = msg.Height
		m.viewport.Width = m.width - 4
		m.viewport.Height = m.height - 8
		m.inputComponent.SetSize(m.width-4, 1)
		m.messageRenderer.SetWidth(m.width - 4)
		m.layout.SetSize(m.width, m.height)
		m.updateViewport()

	case tea.KeyMsg:
		switch msg.String() {
		case "ctrl+c":
			return m, tea.Quit
		case "enter":
			if m.inputComponent.GetValue() != "" && !m.loading {
				return m.sendMessage()
			}
		case "ctrl+d":
			m.showDiff = !m.showDiff
		case "ctrl+t":
			m.showTools = !m.showTools
		case "ctrl+r":
			// Regenerate last response
			if len(m.messages) > 1 {
				return m.regenerateResponse()
			}
		case "page_up":
			m.viewport.LineUp(5)
		case "page_down":
			m.viewport.LineDown(5)
		default:
			// Handle input component
			m.inputComponent, cmd = m.inputComponent.Update(msg)
			cmds = append(cmds, cmd)
		}

	case responseMsg:
		m.loading = false
		m.progressComponent.SetProgress(1.0)

		// Create assistant message
		assistantMsg := components.Message{
			ID:        fmt.Sprintf("msg_%d", time.Now().Unix()),
			Type:      components.MessageTypeAssistant,
			Content:   string(msg),
			Timestamp: time.Now(),
			Author:    "Arien",
		}
		m.messages = append(m.messages, assistantMsg)
		m.updateViewport()

	case errorMsg:
		m.loading = false
		m.progressComponent.SetProgress(0.0)

		// Create error message
		errorMessage := components.Message{
			ID:        fmt.Sprintf("error_%d", time.Now().Unix()),
			Type:      components.MessageTypeError,
			Content:   fmt.Sprintf("Error: %v", error(msg)),
			Timestamp: time.Now(),
		}
		m.messages = append(m.messages, errorMessage)
		m.updateViewport()

	case toolExecutionMsg:
		// Handle tool execution updates
		m.handleToolExecution(toolExecutionMsg(msg))

	case progressMsg:
		// Update progress
		m.progressComponent.SetProgress(float64(msg))
	}

	// Update progress component animation
	m.progressComponent.Update()

	return m, tea.Batch(cmds...)
}

// View renders the enhanced UI
func (m chatModel) View() string {
	// Update layout size
	m.layout.SetSize(m.width, m.height)

	// Main chat area
	chatContent := m.viewport.View()
	m.layout.SetCenter(chatContent)

	// Input area
	var inputArea strings.Builder

	// Loading indicator
	if m.loading {
		inputArea.WriteString(m.progressComponent.View())
		inputArea.WriteString("\n")
	}

	// Input field
	inputArea.WriteString(m.inputComponent.View())

	m.layout.SetFooter(inputArea.String())

	// Side panels
	if m.showTools {
		toolsPanel := m.renderToolsPanel()
		m.layout.SetLeft(toolsPanel)
	}

	if m.showDiff && m.diffViewer != nil {
		diffPanel := m.diffViewer.View()
		m.layout.SetRight(diffPanel)
	}

	// Help bar
	helpText := m.renderHelpBar()

	// Combine layout with help
	mainView := m.layout.View()

	return lipgloss.JoinVertical(lipgloss.Left, mainView, helpText)
}

// updateViewport updates the viewport content with all messages
func (m *chatModel) updateViewport() {
	var content strings.Builder

	for i, msg := range m.messages {
		if i > 0 {
			content.WriteString("\n\n")
		}
		rendered := m.messageRenderer.RenderMessage(msg)
		content.WriteString(rendered)
	}

	m.viewport.SetContent(content.String())
	m.viewport.GotoBottom()
}

// sendMessage sends a user message and gets AI response
func (m chatModel) sendMessage() (chatModel, tea.Cmd) {
	userInput := strings.TrimSpace(m.inputComponent.GetValue())
	if userInput == "" {
		return m, nil
	}

	// Add user message
	userMsg := components.Message{
		ID:        fmt.Sprintf("user_%d", time.Now().Unix()),
		Type:      components.MessageTypeUser,
		Content:   userInput,
		Timestamp: time.Now(),
		Author:    "You",
	}
	m.messages = append(m.messages, userMsg)

	// Add to input history
	m.inputComponent.AddToHistory(userInput)

	// Clear input and set loading
	m.inputComponent.SetValue("")
	m.loading = true
	m.progressComponent.SetProgress(0.1)

	// Update viewport
	m.updateViewport()

	// Send to AI
	return m, m.getAIResponse(userInput)
}

// regenerateResponse regenerates the last AI response
func (m chatModel) regenerateResponse() (chatModel, tea.Cmd) {
	if len(m.messages) < 2 {
		return m, nil
	}

	// Find the last user message
	var lastUserMsg components.Message
	for i := len(m.messages) - 1; i >= 0; i-- {
		if m.messages[i].Type == components.MessageTypeUser {
			lastUserMsg = m.messages[i]
			// Remove all messages after this user message
			m.messages = m.messages[:i+1]
			break
		}
	}

	if lastUserMsg.Content == "" {
		return m, nil
	}

	m.loading = true
	m.progressComponent.SetProgress(0.1)
	m.updateViewport()

	return m, m.getAIResponse(lastUserMsg.Content)
}

// sendMessage sends a user message and gets AI response
func (m chatModel) sendMessage() (chatModel, tea.Cmd) {
	userMessage := strings.TrimSpace(m.input)
	if userMessage == "" {
		return m, nil
	}

	// Add user message
	m.messages = append(m.messages, Message{
		Role:    "user",
		Content: userMessage,
		Time:    "now",
	})

	// Clear input and set loading
	m.input = ""
	m.loading = true

	// Send to AI
	return m, m.getAIResponse(userMessage)
}

// getAIResponse gets response from AI engine with progress updates
func (m chatModel) getAIResponse(message string) tea.Cmd {
	return func() tea.Msg {
		ctx := context.Background()

		// Simulate progress updates
		go func() {
			for i := 0.1; i <= 0.9; i += 0.1 {
				time.Sleep(100 * time.Millisecond)
				// In a real implementation, you'd send progress updates
			}
		}()

		response, err := m.engine.ProcessMessage(ctx, message)
		if err != nil {
			return errorMsg(err)
		}
		return responseMsg(response.Content)
	}
}

// renderToolsPanel renders the tools sidebar
func (m chatModel) renderToolsPanel() string {
	var content strings.Builder

	titleStyle := m.theme.Styles.Subtitle.Copy().
		Foreground(m.theme.Colors.Primary)

	content.WriteString(titleStyle.Render("🔧 Available Tools"))
	content.WriteString("\n\n")

	tools := []string{
		"ls - List files",
		"read - Read files",
		"write - Write files",
		"grep - Search text",
		"search - Find files",
		"shell - Run commands",
		"edit - Edit files",
		"diff - View changes",
		"web-search - Search web",
		"memory - Save info",
	}

	bodyStyle := m.theme.Styles.Body.Copy().
		Foreground(m.theme.Colors.TextSecondary)

	for _, tool := range tools {
		content.WriteString(bodyStyle.Render("• " + tool))
		content.WriteString("\n")
	}

	return content.String()
}

// renderHelpBar renders the help bar
func (m chatModel) renderHelpBar() string {
	helpItems := []string{
		"enter: send",
		"ctrl+c: quit",
		"ctrl+t: toggle tools",
		"ctrl+d: toggle diff",
		"ctrl+r: regenerate",
		"pg up/dn: scroll",
	}

	helpStyle := m.theme.Styles.Caption.Copy().
		Foreground(m.theme.Colors.TextMuted).
		Background(m.theme.Colors.Surface).
		Padding(0, 1).
		Width(m.width)

	return helpStyle.Render(strings.Join(helpItems, " • "))
}

// handleToolExecution handles tool execution updates
func (m *chatModel) handleToolExecution(msg toolExecutionMsg) {
	// Update the last message with tool execution info
	if len(m.messages) > 0 {
		lastMsg := &m.messages[len(m.messages)-1]
		if lastMsg.Type == components.MessageTypeAssistant {
			// Add tool call information
			toolCall := components.ToolCall{
				Name:     msg.ToolName,
				Status:   msg.Status,
				Duration: msg.Duration,
				Result:   msg.Result,
			}
			lastMsg.Tools = append(lastMsg.Tools, toolCall)
			m.updateViewport()
		}
	}
}

// Message types for tea.Cmd
type responseMsg string
type errorMsg error
type progressMsg float64
type toolExecutionMsg struct {
	ToolName string
	Status   string
	Duration time.Duration
	Result   string
}

// Note: Styles are now handled by the theme system and components
// This provides a more consistent and customizable UI experience with:
// - Theme-aware styling
// - Component-based architecture
// - Enhanced message rendering
// - Real-time diff viewing
// - Tool integration panels
// - Progress indicators
// - Advanced input handling
