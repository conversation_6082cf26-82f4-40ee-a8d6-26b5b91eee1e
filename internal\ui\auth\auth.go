/**
 * @license
 * Copyright 2025 Arien LLC
 * License: MIT
 */

package auth

import (
	"context"
	"fmt"
	"strings"

	"arien/internal/config"
	"arien/internal/core"
	"arien/internal/ui/components"
	"arien/internal/ui/themes"

	tea "github.com/charmbracelet/bubbletea"
	"github.com/charmbracelet/lipgloss"
	"github.com/charmbracelet/log"
)

// AuthApp handles the authentication setup flow
type AuthApp struct {
	engine *core.Engine
	logger *log.Logger
}

// authModel represents the enhanced authentication UI model
type authModel struct {
	engine          *core.Engine
	logger          *log.Logger
	step            authStep
	provider        string
	apiKey          string
	model           string
	selectedTheme   string
	cursor          int
	err             error
	done            bool
	width           int
	height          int
	currentTheme    themes.Theme
	showThemePreview bool

	// Components
	inputComponent  *components.InputComponent
	progressComponent *components.ProgressComponent
}

type authStep int

const (
	stepWelcome authStep = iota
	stepProvider
	stepAPIKey
	stepModel
	stepTheme
	stepComplete
)

// NewAuthApp creates a new authentication app
func NewAuthApp(engine *core.Engine, logger *log.Logger) *AuthApp {
	return &AuthApp{
		engine: engine,
		logger: logger,
	}
}

// Run starts the enhanced authentication flow
func (a *AuthApp) Run(ctx context.Context) error {
	model := authModel{
		engine:        a.engine,
		logger:        a.logger,
		step:          stepWelcome,
		width:         100,
		height:        30,
		currentTheme:  themes.GetDefaultTheme(),
		selectedTheme: "dark-pro",
	}

	// Initialize components
	model.inputComponent = components.NewInputComponent(components.InputTypeText, model.currentTheme)
	model.progressComponent = components.NewProgressComponent(components.ProgressTypeSteps, model.currentTheme)

	// Setup progress steps
	model.progressComponent.
		AddStep("Welcome", "Introduction to Arien").
		AddStep("Provider", "Select AI provider").
		AddStep("API Key", "Configure authentication").
		AddStep("Model", "Choose AI model").
		AddStep("Theme", "Customize appearance").
		AddStep("Complete", "Finish setup")

	p := tea.NewProgram(model, tea.WithAltScreen())

	if _, err := p.Run(); err != nil {
		return fmt.Errorf("authentication UI failed: %w", err)
	}

	return nil
}

// Init initializes the model
func (m authModel) Init() tea.Cmd {
	return nil
}

// Update handles messages with enhanced functionality
func (m authModel) Update(msg tea.Msg) (tea.Model, tea.Cmd) {
	var cmd tea.Cmd

	switch msg := msg.(type) {
	case tea.WindowSizeMsg:
		m.width = msg.Width
		m.height = msg.Height
		m.inputComponent.SetSize(m.width-20, 1)
		m.progressComponent.SetSize(m.width-10, 3)

	case tea.KeyMsg:
		switch msg.String() {
		case "ctrl+c", "q":
			return m, tea.Quit
		case "enter":
			return m.handleEnter()
		case "up", "k":
			if m.step == stepTheme && m.showThemePreview {
				m.cursor = (m.cursor - 1 + 5) % 5 // 5 themes
				m.updateThemePreview()
			} else if m.cursor > 0 {
				m.cursor--
			}
		case "down", "j":
			if m.step == stepTheme && m.showThemePreview {
				m.cursor = (m.cursor + 1) % 5 // 5 themes
				m.updateThemePreview()
			} else {
				m.cursor++
			}
		case "tab":
			if m.step == stepTheme {
				m.showThemePreview = !m.showThemePreview
			}
		default:
			// Handle input component updates
			if m.step == stepAPIKey {
				m.inputComponent, cmd = m.inputComponent.Update(msg)
				m.apiKey = m.inputComponent.GetValue()
			}
		}
	}

	// Update progress
	m.progressComponent.SetStepStatus(int(m.step), components.StepStatusActive)
	for i := 0; i < int(m.step); i++ {
		m.progressComponent.SetStepStatus(i, components.StepStatusComplete)
	}

	return m, cmd
}

// View renders the enhanced UI
func (m authModel) View() string {
	if m.done {
		return m.renderComplete()
	}

	// Create main layout
	layout := components.NewLayoutComponent(components.LayoutTypeBorder, m.currentTheme)
	layout.SetSize(m.width, m.height)
	layout.SetTitle("🤖 Arien Setup")

	// Header with progress
	progressView := m.progressComponent.View()
	layout.SetHeader(progressView)

	// Main content based on step
	var content string
	switch m.step {
	case stepWelcome:
		content = m.renderWelcome()
	case stepProvider:
		content = m.renderProviderSelection()
	case stepAPIKey:
		content = m.renderAPIKeyInput()
	case stepModel:
		content = m.renderModelSelection()
	case stepTheme:
		content = m.renderThemeSelection()
	default:
		content = "Unknown step"
	}

	layout.SetCenter(content)

	// Footer with help
	footer := m.renderFooter()
	layout.SetFooter(footer)

	// Theme preview sidebar
	if m.step == stepTheme && m.showThemePreview {
		preview := m.renderThemePreview()
		layout.SetRight(preview)
	}

	return layout.View()
}

// handleEnter processes enter key based on current step
func (m authModel) handleEnter() (authModel, tea.Cmd) {
	switch m.step {
	case stepWelcome:
		m.step = stepProvider
		m.cursor = 0
	case stepProvider:
		providers := []string{"deepseek", "openai", "gemini", "anthropic"}
		if m.cursor < len(providers) {
			m.provider = providers[m.cursor]
			m.step = stepAPIKey
			m.cursor = 0
			m.inputComponent.SetFocus(true)
			m.inputComponent.SetPlaceholder("Enter your " + m.provider + " API key...")
		}
	case stepAPIKey:
		if m.apiKey != "" && m.inputComponent.IsValid() {
			m.step = stepModel
			m.cursor = 0
			m.inputComponent.SetFocus(false)
		}
	case stepModel:
		models := m.getModelsForProvider()
		if m.cursor < len(models) {
			m.model = models[m.cursor]
			m.step = stepTheme
			m.cursor = 0
		}
	case stepTheme:
		themes := []string{"dark-pro", "dark-soft", "light", "high-contrast", "colorblind"}
		if m.cursor < len(themes) {
			m.selectedTheme = themes[m.cursor]
			m.step = stepComplete
			if err := m.saveConfiguration(); err != nil {
				m.err = err
			} else {
				m.done = true
				return m, tea.Quit
			}
		}
	}
	return m, nil
}

// updateThemePreview updates the current theme for preview
func (m *authModel) updateThemePreview() {
	themes := []string{"dark-pro", "dark-soft", "light", "high-contrast", "colorblind"}
	if m.cursor < len(themes) {
		if theme, exists := themes.GetTheme(themes[m.cursor]); exists {
			m.currentTheme = theme
			m.inputComponent.SetTheme(theme)
			m.progressComponent = components.NewProgressComponent(components.ProgressTypeSteps, theme)
		}
	}
}

// renderWelcome renders the welcome screen
func (m authModel) renderWelcome() string {
	var s strings.Builder

	titleStyle := m.currentTheme.Styles.Title.Copy().
		Foreground(m.currentTheme.Colors.Primary).
		Align(lipgloss.Center).
		Width(m.width - 20)

	s.WriteString(titleStyle.Render("Welcome to Arien"))
	s.WriteString("\n\n")

	bodyStyle := m.currentTheme.Styles.Body.Copy().
		Foreground(m.currentTheme.Colors.Text).
		Width(m.width - 20).
		Align(lipgloss.Center)

	welcome := `Arien is your elite AI-powered software engineering assistant with advanced reasoning capabilities, comprehensive tool orchestration, and deep expertise across all programming languages and frameworks.

Features:
• Multi-LLM provider support (DeepSeek, OpenAI, Gemini, Anthropic)
• 19+ built-in tools for development, debugging, and automation
• Real-time diff viewing and code analysis
• Intelligent task management and workflow automation
• Modern terminal interface with customizable themes

Let's get you set up in just a few steps!`

	s.WriteString(bodyStyle.Render(welcome))
	s.WriteString("\n\n")

	buttonStyle := m.currentTheme.Styles.ButtonActive.Copy().
		Align(lipgloss.Center).
		Width(20)

	s.WriteString(buttonStyle.Render("Get Started"))

	return s.String()
}

// renderProviderSelection renders the enhanced provider selection screen
func (m authModel) renderProviderSelection() string {
	var s strings.Builder

	titleStyle := m.currentTheme.Styles.Subtitle.Copy().
		Foreground(m.currentTheme.Colors.Primary)

	s.WriteString(titleStyle.Render("Select AI Provider"))
	s.WriteString("\n\n")

	bodyStyle := m.currentTheme.Styles.Body.Copy().
		Foreground(m.currentTheme.Colors.Text)

	s.WriteString(bodyStyle.Render("Choose your preferred AI provider. You can change this later in settings."))
	s.WriteString("\n\n")

	providers := []string{"DeepSeek", "OpenAI", "Google Gemini", "Anthropic Claude"}
	descriptions := []string{
		"Advanced reasoning models with competitive pricing",
		"Industry-leading GPT-4 and GPT-3.5 models",
		"Google's powerful Gemini Pro models",
		"Anthropic's Claude models with strong safety focus",
	}
	icons := []string{"🧠", "🤖", "🔮", "🛡️"}

	for i := range providers {
		var style lipgloss.Style
		if m.cursor == i {
			style = m.currentTheme.Styles.Selection.Copy().
				Foreground(m.currentTheme.Colors.Text).
				Background(m.currentTheme.Colors.Primary).
				Padding(1).
				Margin(0, 1).
				Width(m.width - 25)
		} else {
			style = m.currentTheme.Styles.Card.Copy().
				Foreground(m.currentTheme.Colors.TextSecondary).
				Padding(1).
				Margin(0, 1).
				Width(m.width - 25)
		}

		content := fmt.Sprintf("%s %s\n%s", icons[i], providers[i], descriptions[i])
		s.WriteString(style.Render(content))
		s.WriteString("\n")
	}

	return s.String()
}

// renderAPIKeyInput renders the enhanced API key input screen
func (m authModel) renderAPIKeyInput() string {
	var s strings.Builder

	titleStyle := m.currentTheme.Styles.Subtitle.Copy().
		Foreground(m.currentTheme.Colors.Primary)

	s.WriteString(titleStyle.Render("🔑 API Key Configuration"))
	s.WriteString("\n\n")

	// Provider info
	providerStyle := m.currentTheme.Styles.Code.Copy().
		Foreground(m.currentTheme.Colors.Secondary).
		Background(m.currentTheme.Colors.Surface).
		Padding(0, 1)

	s.WriteString("Provider: ")
	s.WriteString(providerStyle.Render(m.provider))
	s.WriteString("\n\n")

	// Instructions
	bodyStyle := m.currentTheme.Styles.Body.Copy().
		Foreground(m.currentTheme.Colors.Text)

	instructions := m.getAPIKeyInstructions()
	s.WriteString(bodyStyle.Render(instructions))
	s.WriteString("\n\n")

	// Input field
	m.inputComponent.SetInputType(components.InputTypePassword)
	s.WriteString(m.inputComponent.View())

	if m.err != nil {
		s.WriteString("\n\n")
		errorStyle := m.currentTheme.Styles.Error
		s.WriteString(errorStyle.Render(fmt.Sprintf("⚠ Error: %v", m.err)))
	}

	return s.String()
}

// getAPIKeyInstructions returns instructions for getting API keys
func (m authModel) getAPIKeyInstructions() string {
	switch m.provider {
	case "deepseek":
		return "Get your API key from https://platform.deepseek.com/api_keys\nYour key should start with 'sk-'"
	case "openai":
		return "Get your API key from https://platform.openai.com/api-keys\nYour key should start with 'sk-'"
	case "gemini":
		return "Get your API key from https://makersuite.google.com/app/apikey\nYour key should start with 'AI'"
	case "anthropic":
		return "Get your API key from https://console.anthropic.com/\nYour key should start with 'sk-ant-'"
	default:
		return "Please enter your API key for " + m.provider
	}
}

// renderModelSelection renders the enhanced model selection screen
func (m authModel) renderModelSelection() string {
	var s strings.Builder

	titleStyle := m.currentTheme.Styles.Subtitle.Copy().
		Foreground(m.currentTheme.Colors.Primary)

	s.WriteString(titleStyle.Render("🎯 Model Selection"))
	s.WriteString("\n\n")

	// Provider info
	providerStyle := m.currentTheme.Styles.Code.Copy().
		Foreground(m.currentTheme.Colors.Secondary).
		Background(m.currentTheme.Colors.Surface).
		Padding(0, 1)

	s.WriteString("Provider: ")
	s.WriteString(providerStyle.Render(m.provider))
	s.WriteString("\n\n")

	bodyStyle := m.currentTheme.Styles.Body.Copy().
		Foreground(m.currentTheme.Colors.Text)

	s.WriteString(bodyStyle.Render("Choose the AI model that best fits your needs:"))
	s.WriteString("\n\n")

	models := m.getModelsForProvider()
	descriptions := m.getModelDescriptions()

	for i, model := range models {
		var style lipgloss.Style
		if m.cursor == i {
			style = m.currentTheme.Styles.Selection.Copy().
				Foreground(m.currentTheme.Colors.Text).
				Background(m.currentTheme.Colors.Primary).
				Padding(1).
				Margin(0, 1).
				Width(m.width - 25)
		} else {
			style = m.currentTheme.Styles.Card.Copy().
				Foreground(m.currentTheme.Colors.TextSecondary).
				Padding(1).
				Margin(0, 1).
				Width(m.width - 25)
		}

		content := fmt.Sprintf("%s\n%s", model, descriptions[i])
		s.WriteString(style.Render(content))
		s.WriteString("\n")
	}

	return s.String()
}

// renderThemeSelection renders the theme selection screen
func (m authModel) renderThemeSelection() string {
	var s strings.Builder

	titleStyle := m.currentTheme.Styles.Subtitle.Copy().
		Foreground(m.currentTheme.Colors.Primary)

	s.WriteString(titleStyle.Render("🎨 Theme Selection"))
	s.WriteString("\n\n")

	bodyStyle := m.currentTheme.Styles.Body.Copy().
		Foreground(m.currentTheme.Colors.Text)

	s.WriteString(bodyStyle.Render("Choose your preferred theme. You can see a live preview on the right:"))
	s.WriteString("\n\n")

	themeNames := []string{"Dark Pro", "Dark Soft", "Light", "High Contrast", "Color Blind Friendly"}
	themeDescs := []string{
		"Professional dark theme with blue accents",
		"Soft dark theme with green accents",
		"Clean light theme for bright environments",
		"High contrast theme for accessibility",
		"Accessible theme for color vision deficiency",
	}
	themeIcons := []string{"🌙", "🌿", "☀️", "⚡", "🌈"}

	for i := range themeNames {
		var style lipgloss.Style
		if m.cursor == i {
			style = m.currentTheme.Styles.Selection.Copy().
				Foreground(m.currentTheme.Colors.Text).
				Background(m.currentTheme.Colors.Primary).
				Padding(1).
				Margin(0, 1).
				Width(m.width - 45) // Leave space for preview
		} else {
			style = m.currentTheme.Styles.Card.Copy().
				Foreground(m.currentTheme.Colors.TextSecondary).
				Padding(1).
				Margin(0, 1).
				Width(m.width - 45)
		}

		content := fmt.Sprintf("%s %s\n%s", themeIcons[i], themeNames[i], themeDescs[i])
		s.WriteString(style.Render(content))
		s.WriteString("\n")
	}

	if !m.showThemePreview {
		s.WriteString("\n")
		helpStyle := m.currentTheme.Styles.Caption.Copy().
			Foreground(m.currentTheme.Colors.TextMuted)
		s.WriteString(helpStyle.Render("Press Tab to toggle theme preview"))
	}

	return s.String()
}

// renderThemePreview renders the theme preview sidebar
func (m authModel) renderThemePreview() string {
	var s strings.Builder

	titleStyle := m.currentTheme.Styles.Subtitle.Copy().
		Foreground(m.currentTheme.Colors.Primary)

	s.WriteString(titleStyle.Render("Live Preview"))
	s.WriteString("\n\n")

	// Sample UI elements with current theme
	s.WriteString(m.currentTheme.Styles.Header.Render("Header"))
	s.WriteString("\n")
	s.WriteString(m.currentTheme.Styles.Body.Render("Body text"))
	s.WriteString("\n")
	s.WriteString(m.currentTheme.Styles.Code.Render("code"))
	s.WriteString("\n")
	s.WriteString(m.currentTheme.Styles.Success.Render("Success"))
	s.WriteString("\n")
	s.WriteString(m.currentTheme.Styles.Warning.Render("Warning"))
	s.WriteString("\n")
	s.WriteString(m.currentTheme.Styles.Error.Render("Error"))
	s.WriteString("\n")
	s.WriteString(m.currentTheme.Styles.Info.Render("Info"))

	return s.String()
}

// renderFooter renders the footer with help text
func (m authModel) renderFooter() string {
	helpStyle := m.currentTheme.Styles.Caption.Copy().
		Foreground(m.currentTheme.Colors.TextMuted).
		Align(lipgloss.Center)

	switch m.step {
	case stepWelcome:
		return helpStyle.Render("enter: continue • q: quit")
	case stepProvider, stepModel, stepTheme:
		return helpStyle.Render("↑/↓: navigate • enter: select • q: quit")
	case stepAPIKey:
		return helpStyle.Render("type: enter API key • enter: continue • q: quit")
	default:
		return helpStyle.Render("q: quit")
	}
}

// renderComplete renders the enhanced completion screen
func (m authModel) renderComplete() string {
	var s strings.Builder

	titleStyle := m.currentTheme.Styles.Title.Copy().
		Foreground(m.currentTheme.Colors.Success).
		Align(lipgloss.Center).
		Width(m.width - 20)

	s.WriteString(titleStyle.Render("✅ Setup Complete!"))
	s.WriteString("\n\n")

	bodyStyle := m.currentTheme.Styles.Body.Copy().
		Foreground(m.currentTheme.Colors.Text).
		Width(m.width - 20).
		Align(lipgloss.Center)

	summary := fmt.Sprintf(`Arien has been configured successfully!

Configuration Summary:
• Provider: %s
• Model: %s
• Theme: %s

You can now start using Arien with:
  arien chat          # Interactive chat mode
  arien exec "query"  # Direct command execution
  arien tools         # List available tools

Welcome to the future of AI-assisted development!`,
		m.provider, m.model, m.selectedTheme)

	s.WriteString(bodyStyle.Render(summary))
	s.WriteString("\n\n")

	buttonStyle := m.currentTheme.Styles.ButtonActive.Copy().
		Align(lipgloss.Center).
		Width(20)

	s.WriteString(buttonStyle.Render("Start Arien"))

	return s.String()
}

// getModelsForProvider returns available models for the selected provider
func (m authModel) getModelsForProvider() []string {
	switch m.provider {
	case "deepseek":
		return []string{"deepseek-chat", "deepseek-reasoner"}
	case "openai":
		return []string{"gpt-4", "gpt-4-turbo", "gpt-3.5-turbo"}
	case "gemini":
		return []string{"gemini-pro", "gemini-pro-vision"}
	case "anthropic":
		return []string{"claude-3-opus-20240229", "claude-3-sonnet-20240229", "claude-3-haiku-20240307"}
	default:
		return []string{}
	}
}

// getModelDescriptions returns descriptions for the models
func (m authModel) getModelDescriptions() []string {
	switch m.provider {
	case "deepseek":
		return []string{
			"General purpose chat model with strong reasoning",
			"Advanced reasoning model for complex problems",
		}
	case "openai":
		return []string{
			"Most capable GPT-4 model for complex tasks",
			"Faster GPT-4 variant with improved performance",
			"Cost-effective model for simpler tasks",
		}
	case "gemini":
		return []string{
			"Google's flagship model for text and reasoning",
			"Multimodal model with vision capabilities",
		}
	case "anthropic":
		return []string{
			"Most capable Claude model for complex reasoning",
			"Balanced model for most use cases",
			"Fast and efficient model for simple tasks",
		}
	default:
		return []string{}
	}
}

// saveConfiguration saves the enhanced configuration
func (m authModel) saveConfiguration() error {
	providerConfig := config.ProviderConfig{
		APIKey: m.apiKey,
		Model:  m.model,
	}

	// Save provider configuration
	if err := m.engine.Config().SetProvider(m.provider, providerConfig); err != nil {
		return err
	}

	// Save theme configuration
	if err := m.engine.Config().SetTheme(m.selectedTheme); err != nil {
		return err
	}

	// Mark as configured
	return m.engine.Config().SetConfigured(true)
}

// Note: Styles are now handled by the theme system and components
// This provides a more consistent and customizable UI experience
