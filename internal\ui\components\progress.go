/**
 * @license
 * Copyright 2025 Arien LLC
 * License: MIT
 */

package components

import (
	"fmt"
	"strings"
	"time"

	"arien/internal/ui/themes"

	"github.com/charmbracelet/lipgloss"
)

// ProgressType represents different types of progress indicators
type ProgressType int

const (
	ProgressTypeBar ProgressType = iota
	ProgressTypeSpinner
	ProgressTypeCircular
	ProgressTypeDots
	ProgressTypeSteps
)

// ProgressComponent represents a progress indicator component
type ProgressComponent struct {
	// Core properties
	progressType ProgressType
	theme        themes.Theme
	
	// Progress state
	current      float64 // 0.0 to 1.0
	total        float64
	value        float64
	
	// Appearance
	width        int
	height       int
	showPercent  bool
	showValue    bool
	showETA      bool
	
	// Labels
	title        string
	description  string
	units        string
	
	// Animation
	animFrame    int
	lastUpdate   time.Time
	startTime    time.Time
	
	// Steps (for step progress)
	steps        []ProgressStep
	currentStep  int
	
	// Customization
	fillChar     string
	emptyChar    string
	colors       ProgressColors
}

// ProgressStep represents a step in step-based progress
type ProgressStep struct {
	Name        string
	Description string
	Status      StepStatus
	Duration    time.Duration
}

// StepStatus represents the status of a progress step
type StepStatus int

const (
	StepStatusPending StepStatus = iota
	StepStatusActive
	StepStatusComplete
	StepStatusError
	StepStatusSkipped
)

// ProgressColors defines custom colors for progress indicators
type ProgressColors struct {
	Fill       lipgloss.Color
	Empty      lipgloss.Color
	Text       lipgloss.Color
	Background lipgloss.Color
	Success    lipgloss.Color
	Error      lipgloss.Color
	Warning    lipgloss.Color
}

// NewProgressComponent creates a new progress component
func NewProgressComponent(progressType ProgressType, theme themes.Theme) *ProgressComponent {
	return &ProgressComponent{
		progressType: progressType,
		theme:        theme,
		width:        40,
		height:       1,
		showPercent:  true,
		showValue:    false,
		showETA:      false,
		fillChar:     "█",
		emptyChar:    "░",
		startTime:    time.Now(),
		lastUpdate:   time.Now(),
		colors: ProgressColors{
			Fill:       theme.Colors.Primary,
			Empty:      theme.Colors.Surface,
			Text:       theme.Colors.Text,
			Background: theme.Colors.Background,
			Success:    theme.Colors.Success,
			Error:      theme.Colors.Error,
			Warning:    theme.Colors.Warning,
		},
	}
}

// SetProgress sets the current progress (0.0 to 1.0)
func (pc *ProgressComponent) SetProgress(progress float64) *ProgressComponent {
	if progress < 0 {
		progress = 0
	}
	if progress > 1 {
		progress = 1
	}
	pc.current = progress
	pc.lastUpdate = time.Now()
	return pc
}

// SetValue sets the current value and total
func (pc *ProgressComponent) SetValue(value, total float64) *ProgressComponent {
	pc.value = value
	pc.total = total
	if total > 0 {
		pc.current = value / total
	}
	pc.lastUpdate = time.Now()
	return pc
}

// SetSize sets the component size
func (pc *ProgressComponent) SetSize(width, height int) *ProgressComponent {
	pc.width = width
	pc.height = height
	return pc
}

// SetTitle sets the progress title
func (pc *ProgressComponent) SetTitle(title string) *ProgressComponent {
	pc.title = title
	return pc
}

// SetDescription sets the progress description
func (pc *ProgressComponent) SetDescription(description string) *ProgressComponent {
	pc.description = description
	return pc
}

// SetUnits sets the units for value display
func (pc *ProgressComponent) SetUnits(units string) *ProgressComponent {
	pc.units = units
	return pc
}

// SetDisplayOptions configures what to display
func (pc *ProgressComponent) SetDisplayOptions(showPercent, showValue, showETA bool) *ProgressComponent {
	pc.showPercent = showPercent
	pc.showValue = showValue
	pc.showETA = showETA
	return pc
}

// SetCustomChars sets custom fill and empty characters
func (pc *ProgressComponent) SetCustomChars(fillChar, emptyChar string) *ProgressComponent {
	pc.fillChar = fillChar
	pc.emptyChar = emptyChar
	return pc
}

// SetColors sets custom colors
func (pc *ProgressComponent) SetColors(colors ProgressColors) *ProgressComponent {
	pc.colors = colors
	return pc
}

// AddStep adds a step for step-based progress
func (pc *ProgressComponent) AddStep(name, description string) *ProgressComponent {
	step := ProgressStep{
		Name:        name,
		Description: description,
		Status:      StepStatusPending,
	}
	pc.steps = append(pc.steps, step)
	return pc
}

// SetStepStatus updates the status of a specific step
func (pc *ProgressComponent) SetStepStatus(stepIndex int, status StepStatus) *ProgressComponent {
	if stepIndex >= 0 && stepIndex < len(pc.steps) {
		pc.steps[stepIndex].Status = status
		if status == StepStatusActive {
			pc.currentStep = stepIndex
		}
	}
	return pc
}

// NextStep advances to the next step
func (pc *ProgressComponent) NextStep() *ProgressComponent {
	if pc.currentStep < len(pc.steps) {
		// Mark current step as complete
		if pc.currentStep >= 0 && pc.currentStep < len(pc.steps) {
			pc.steps[pc.currentStep].Status = StepStatusComplete
		}
		
		// Move to next step
		pc.currentStep++
		if pc.currentStep < len(pc.steps) {
			pc.steps[pc.currentStep].Status = StepStatusActive
		}
		
		// Update overall progress
		pc.current = float64(pc.currentStep) / float64(len(pc.steps))
	}
	return pc
}

// Update updates the animation frame
func (pc *ProgressComponent) Update() *ProgressComponent {
	pc.animFrame++
	if pc.animFrame > 100 {
		pc.animFrame = 0
	}
	return pc
}

// View renders the progress component
func (pc *ProgressComponent) View() string {
	var result strings.Builder

	// Title
	if pc.title != "" {
		titleStyle := pc.theme.Styles.Subtitle.Copy().
			Foreground(pc.colors.Text).
			MarginBottom(1)
		result.WriteString(titleStyle.Render(pc.title))
		result.WriteString("\n")
	}

	// Main progress indicator
	switch pc.progressType {
	case ProgressTypeBar:
		result.WriteString(pc.renderProgressBar())
	case ProgressTypeSpinner:
		result.WriteString(pc.renderSpinner())
	case ProgressTypeCircular:
		result.WriteString(pc.renderCircular())
	case ProgressTypeDots:
		result.WriteString(pc.renderDots())
	case ProgressTypeSteps:
		result.WriteString(pc.renderSteps())
	}

	// Description
	if pc.description != "" {
		result.WriteString("\n")
		descStyle := pc.theme.Styles.Caption.Copy().
			Foreground(pc.theme.Colors.TextMuted).
			MarginTop(1)
		result.WriteString(descStyle.Render(pc.description))
	}

	// Additional info
	info := pc.renderInfo()
	if info != "" {
		result.WriteString("\n")
		result.WriteString(info)
	}

	return result.String()
}

// renderProgressBar renders a progress bar
func (pc *ProgressComponent) renderProgressBar() string {
	barWidth := pc.width - 2 // Account for brackets
	if barWidth < 1 {
		barWidth = 1
	}

	filled := int(pc.current * float64(barWidth))
	empty := barWidth - filled

	fillStyle := lipgloss.NewStyle().
		Foreground(pc.colors.Fill).
		Background(pc.colors.Fill)
	
	emptyStyle := lipgloss.NewStyle().
		Foreground(pc.colors.Empty).
		Background(pc.colors.Empty)

	bracketStyle := lipgloss.NewStyle().
		Foreground(pc.colors.Text)

	bar := fillStyle.Render(strings.Repeat(pc.fillChar, filled)) +
		emptyStyle.Render(strings.Repeat(pc.emptyChar, empty))

	return bracketStyle.Render("[") + bar + bracketStyle.Render("]")
}

// renderSpinner renders a spinning indicator
func (pc *ProgressComponent) renderSpinner() string {
	spinners := []string{"⠋", "⠙", "⠹", "⠸", "⠼", "⠴", "⠦", "⠧", "⠇", "⠏"}
	frame := pc.animFrame % len(spinners)
	
	spinnerStyle := lipgloss.NewStyle().
		Foreground(pc.colors.Fill).
		Bold(true)

	return spinnerStyle.Render(spinners[frame])
}

// renderCircular renders a circular progress indicator
func (pc *ProgressComponent) renderCircular() string {
	// Simple circular representation using Unicode characters
	segments := []string{"◐", "◓", "◑", "◒"}
	
	if pc.current >= 1.0 {
		return lipgloss.NewStyle().
			Foreground(pc.colors.Success).
			Render("●")
	}
	
	frame := int(pc.current * 4) % len(segments)
	
	circularStyle := lipgloss.NewStyle().
		Foreground(pc.colors.Fill).
		Bold(true)

	return circularStyle.Render(segments[frame])
}

// renderDots renders a dots progress indicator
func (pc *ProgressComponent) renderDots() string {
	dots := int(pc.current * float64(pc.width))
	if dots > pc.width {
		dots = pc.width
	}

	fillStyle := lipgloss.NewStyle().
		Foreground(pc.colors.Fill)
	
	emptyStyle := lipgloss.NewStyle().
		Foreground(pc.colors.Empty)

	filled := fillStyle.Render(strings.Repeat("●", dots))
	empty := emptyStyle.Render(strings.Repeat("○", pc.width-dots))

	return filled + empty
}

// renderSteps renders step-based progress
func (pc *ProgressComponent) renderSteps() string {
	if len(pc.steps) == 0 {
		return ""
	}

	var result strings.Builder

	for i, step := range pc.steps {
		if i > 0 {
			result.WriteString("\n")
		}

		// Step indicator
		indicator := pc.getStepIndicator(step.Status)
		result.WriteString(indicator)
		result.WriteString(" ")

		// Step name
		nameStyle := pc.getStepNameStyle(step.Status)
		result.WriteString(nameStyle.Render(step.Name))

		// Step description
		if step.Description != "" {
			result.WriteString(" - ")
			descStyle := pc.theme.Styles.Caption.Copy().
				Foreground(pc.theme.Colors.TextMuted)
			result.WriteString(descStyle.Render(step.Description))
		}
	}

	return result.String()
}

// renderInfo renders additional information (percentage, value, ETA)
func (pc *ProgressComponent) renderInfo() string {
	var parts []string

	// Percentage
	if pc.showPercent {
		percent := pc.current * 100
		parts = append(parts, fmt.Sprintf("%.1f%%", percent))
	}

	// Value
	if pc.showValue && pc.total > 0 {
		if pc.units != "" {
			parts = append(parts, fmt.Sprintf("%.0f/%.0f %s", pc.value, pc.total, pc.units))
		} else {
			parts = append(parts, fmt.Sprintf("%.0f/%.0f", pc.value, pc.total))
		}
	}

	// ETA
	if pc.showETA && pc.current > 0 && pc.current < 1 {
		elapsed := time.Since(pc.startTime)
		estimated := time.Duration(float64(elapsed) / pc.current)
		remaining := estimated - elapsed

		if remaining > 0 {
			parts = append(parts, fmt.Sprintf("ETA: %v", remaining.Round(time.Second)))
		}
	}

	if len(parts) == 0 {
		return ""
	}

	infoStyle := pc.theme.Styles.Caption.Copy().
		Foreground(pc.theme.Colors.TextSecondary).
		MarginTop(1)

	return infoStyle.Render(strings.Join(parts, " • "))
}

// getStepIndicator returns the indicator for a step status
func (pc *ProgressComponent) getStepIndicator(status StepStatus) string {
	switch status {
	case StepStatusPending:
		return lipgloss.NewStyle().
			Foreground(pc.colors.Empty).
			Render("○")
	case StepStatusActive:
		return lipgloss.NewStyle().
			Foreground(pc.colors.Fill).
			Bold(true).
			Render("◐")
	case StepStatusComplete:
		return lipgloss.NewStyle().
			Foreground(pc.colors.Success).
			Bold(true).
			Render("●")
	case StepStatusError:
		return lipgloss.NewStyle().
			Foreground(pc.colors.Error).
			Bold(true).
			Render("✗")
	case StepStatusSkipped:
		return lipgloss.NewStyle().
			Foreground(pc.colors.Warning).
			Render("⊘")
	default:
		return "○"
	}
}

// getStepNameStyle returns the style for a step name based on status
func (pc *ProgressComponent) getStepNameStyle(status StepStatus) lipgloss.Style {
	switch status {
	case StepStatusPending:
		return pc.theme.Styles.Body.Copy().
			Foreground(pc.theme.Colors.TextMuted)
	case StepStatusActive:
		return pc.theme.Styles.Body.Copy().
			Foreground(pc.colors.Fill).
			Bold(true)
	case StepStatusComplete:
		return pc.theme.Styles.Body.Copy().
			Foreground(pc.colors.Success)
	case StepStatusError:
		return pc.theme.Styles.Body.Copy().
			Foreground(pc.colors.Error)
	case StepStatusSkipped:
		return pc.theme.Styles.Body.Copy().
			Foreground(pc.colors.Warning)
	default:
		return pc.theme.Styles.Body
	}
}

// IsComplete returns whether the progress is complete
func (pc *ProgressComponent) IsComplete() bool {
	return pc.current >= 1.0
}

// GetProgress returns the current progress (0.0 to 1.0)
func (pc *ProgressComponent) GetProgress() float64 {
	return pc.current
}

// GetElapsedTime returns the elapsed time since start
func (pc *ProgressComponent) GetElapsedTime() time.Duration {
	return time.Since(pc.startTime)
}

// Reset resets the progress to initial state
func (pc *ProgressComponent) Reset() *ProgressComponent {
	pc.current = 0
	pc.value = 0
	pc.animFrame = 0
	pc.startTime = time.Now()
	pc.lastUpdate = time.Now()
	pc.currentStep = 0

	// Reset all steps to pending
	for i := range pc.steps {
		pc.steps[i].Status = StepStatusPending
	}

	return pc
}
