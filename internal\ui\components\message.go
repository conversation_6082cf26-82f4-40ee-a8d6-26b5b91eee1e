/**
 * @license
 * Copyright 2025 Arien LLC
 * License: MIT
 */

package components

import (
	"fmt"
	"strings"
	"time"

	"arien/internal/ui/themes"

	"github.com/charmbracelet/lipgloss"
)

// MessageType represents different types of messages
type MessageType int

const (
	MessageTypeUser MessageType = iota
	MessageTypeAssistant
	MessageTypeSystem
	MessageTypeError
	MessageTypeWarning
	MessageTypeInfo
	MessageTypeSuccess
	MessageTypeTool
	MessageTypeCode
)

// Message represents a chat message with enhanced features
type Message struct {
	ID        string
	Type      MessageType
	Content   string
	Timestamp time.Time
	Author    string
	Metadata  map[string]interface{}
	IsLoading bool
	Progress  float64
	Tools     []ToolCall
	CodeBlocks []CodeBlock
}

// ToolCall represents a tool function call
type ToolCall struct {
	Name      string
	Arguments map[string]interface{}
	Result    string
	Status    string
	Duration  time.Duration
}

// CodeBlock represents a code block in a message
type CodeBlock struct {
	Language string
	Content  string
	Filename string
	LineStart int
	LineEnd   int
}

// MessageRenderer handles rendering of messages with themes
type MessageRenderer struct {
	theme       themes.Theme
	width       int
	showTime    bool
	showAuthor  bool
	showTools   bool
	showCode    bool
	maxWidth    int
}

// NewMessageRenderer creates a new message renderer
func NewMessageRenderer(theme themes.Theme, width int) *MessageRenderer {
	return &MessageRenderer{
		theme:      theme,
		width:      width,
		showTime:   true,
		showAuthor: true,
		showTools:  true,
		showCode:   true,
		maxWidth:   120,
	}
}

// SetTheme updates the theme
func (mr *MessageRenderer) SetTheme(theme themes.Theme) {
	mr.theme = theme
}

// SetWidth updates the width
func (mr *MessageRenderer) SetWidth(width int) {
	mr.width = width
	if width > mr.maxWidth {
		mr.width = mr.maxWidth
	}
}

// SetOptions configures display options
func (mr *MessageRenderer) SetOptions(showTime, showAuthor, showTools, showCode bool) {
	mr.showTime = showTime
	mr.showAuthor = showAuthor
	mr.showTools = showTools
	mr.showCode = showCode
}

// RenderMessage renders a single message
func (mr *MessageRenderer) RenderMessage(msg Message) string {
	var result strings.Builder

	// Message header
	header := mr.renderHeader(msg)
	if header != "" {
		result.WriteString(header)
		result.WriteString("\n")
	}

	// Message content
	content := mr.renderContent(msg)
	result.WriteString(content)

	// Tool calls
	if mr.showTools && len(msg.Tools) > 0 {
		result.WriteString("\n")
		tools := mr.renderTools(msg.Tools)
		result.WriteString(tools)
	}

	// Code blocks
	if mr.showCode && len(msg.CodeBlocks) > 0 {
		result.WriteString("\n")
		code := mr.renderCodeBlocks(msg.CodeBlocks)
		result.WriteString(code)
	}

	// Loading indicator
	if msg.IsLoading {
		result.WriteString("\n")
		loading := mr.renderLoading(msg.Progress)
		result.WriteString(loading)
	}

	// Wrap in message container
	return mr.wrapMessage(msg, result.String())
}

// renderHeader renders the message header
func (mr *MessageRenderer) renderHeader(msg Message) string {
	if !mr.showTime && !mr.showAuthor {
		return ""
	}

	var parts []string

	// Author/Type
	if mr.showAuthor {
		author := mr.getAuthorDisplay(msg)
		authorStyle := mr.getAuthorStyle(msg.Type)
		parts = append(parts, authorStyle.Render(author))
	}

	// Timestamp
	if mr.showTime {
		timeStr := msg.Timestamp.Format("15:04:05")
		timeStyle := mr.theme.Styles.Caption.Copy().
			Foreground(mr.theme.Colors.TextMuted)
		parts = append(parts, timeStyle.Render(timeStr))
	}

	if len(parts) == 0 {
		return ""
	}

	headerStyle := mr.theme.Styles.Base.Copy().
		Foreground(mr.theme.Colors.TextSecondary).
		MarginBottom(1)

	return headerStyle.Render(strings.Join(parts, " • "))
}

// renderContent renders the main message content
func (mr *MessageRenderer) renderContent(msg Message) string {
	contentStyle := mr.getContentStyle(msg.Type)
	
	// Word wrap the content
	wrapped := mr.wrapText(msg.Content, mr.width-4)
	
	return contentStyle.Render(wrapped)
}

// renderTools renders tool calls
func (mr *MessageRenderer) renderTools(tools []ToolCall) string {
	var result strings.Builder

	toolHeaderStyle := mr.theme.Styles.Subtitle.Copy().
		Foreground(mr.theme.Colors.Info).
		MarginTop(1).
		MarginBottom(1)

	result.WriteString(toolHeaderStyle.Render("🔧 Tool Calls"))
	result.WriteString("\n")

	for i, tool := range tools {
		if i > 0 {
			result.WriteString("\n")
		}
		result.WriteString(mr.renderSingleTool(tool))
	}

	return result.String()
}

// renderSingleTool renders a single tool call
func (mr *MessageRenderer) renderSingleTool(tool ToolCall) string {
	var result strings.Builder

	// Tool name and status
	nameStyle := mr.theme.Styles.Code.Copy().
		Foreground(mr.theme.Colors.Primary)
	
	statusStyle := mr.getStatusStyle(tool.Status)
	
	header := fmt.Sprintf("%s %s", 
		nameStyle.Render(tool.Name),
		statusStyle.Render(tool.Status))
	
	if tool.Duration > 0 {
		durationStyle := mr.theme.Styles.Caption.Copy().
			Foreground(mr.theme.Colors.TextMuted)
		header += " " + durationStyle.Render(fmt.Sprintf("(%v)", tool.Duration))
	}

	result.WriteString(header)
	result.WriteString("\n")

	// Arguments (if any)
	if len(tool.Arguments) > 0 {
		argsStyle := mr.theme.Styles.Body.Copy().
			Foreground(mr.theme.Colors.TextSecondary).
			MarginLeft(2)
		
		result.WriteString(argsStyle.Render("Arguments: " + mr.formatArguments(tool.Arguments)))
		result.WriteString("\n")
	}

	// Result (if any)
	if tool.Result != "" {
		resultStyle := mr.theme.Styles.Body.Copy().
			Foreground(mr.theme.Colors.Text).
			MarginLeft(2)
		
		result.WriteString(resultStyle.Render("Result: " + tool.Result))
	}

	return result.String()
}

// renderCodeBlocks renders code blocks
func (mr *MessageRenderer) renderCodeBlocks(blocks []CodeBlock) string {
	var result strings.Builder

	codeHeaderStyle := mr.theme.Styles.Subtitle.Copy().
		Foreground(mr.theme.Colors.Accent).
		MarginTop(1).
		MarginBottom(1)

	result.WriteString(codeHeaderStyle.Render("💻 Code"))
	result.WriteString("\n")

	for i, block := range blocks {
		if i > 0 {
			result.WriteString("\n")
		}
		result.WriteString(mr.renderSingleCodeBlock(block))
	}

	return result.String()
}

// renderSingleCodeBlock renders a single code block
func (mr *MessageRenderer) renderSingleCodeBlock(block CodeBlock) string {
	var result strings.Builder

	// Code header with language and filename
	headerParts := []string{}
	if block.Language != "" {
		headerParts = append(headerParts, block.Language)
	}
	if block.Filename != "" {
		headerParts = append(headerParts, block.Filename)
	}
	if block.LineStart > 0 {
		if block.LineEnd > block.LineStart {
			headerParts = append(headerParts, fmt.Sprintf("lines %d-%d", block.LineStart, block.LineEnd))
		} else {
			headerParts = append(headerParts, fmt.Sprintf("line %d", block.LineStart))
		}
	}

	if len(headerParts) > 0 {
		headerStyle := mr.theme.Styles.Caption.Copy().
			Foreground(mr.theme.Colors.TextMuted).
			Background(mr.theme.Colors.Surface).
			Padding(0, 1)
		
		result.WriteString(headerStyle.Render(strings.Join(headerParts, " • ")))
		result.WriteString("\n")
	}

	// Code content
	codeStyle := mr.theme.Styles.Code.Copy().
		Background(mr.theme.Colors.Surface).
		Padding(1).
		Width(mr.width - 4)

	result.WriteString(codeStyle.Render(block.Content))

	return result.String()
}

// renderLoading renders a loading indicator
func (mr *MessageRenderer) renderLoading(progress float64) string {
	loadingStyle := mr.theme.Styles.Info.Copy().
		Foreground(mr.theme.Colors.Warning)

	if progress > 0 {
		progressBar := mr.renderProgressBar(progress)
		return loadingStyle.Render("⏳ Processing... " + progressBar)
	}

	return loadingStyle.Render("⏳ Processing...")
}

// getAuthorDisplay returns the display name for the message author
func (mr *MessageRenderer) getAuthorDisplay(msg Message) string {
	if msg.Author != "" {
		return msg.Author
	}

	switch msg.Type {
	case MessageTypeUser:
		return "You"
	case MessageTypeAssistant:
		return "Arien"
	case MessageTypeSystem:
		return "System"
	case MessageTypeError:
		return "Error"
	case MessageTypeWarning:
		return "Warning"
	case MessageTypeInfo:
		return "Info"
	case MessageTypeSuccess:
		return "Success"
	case MessageTypeTool:
		return "Tool"
	case MessageTypeCode:
		return "Code"
	default:
		return "Unknown"
	}
}

// getAuthorStyle returns the style for the author based on message type
func (mr *MessageRenderer) getAuthorStyle(msgType MessageType) lipgloss.Style {
	switch msgType {
	case MessageTypeUser:
		return mr.theme.Styles.Base.Copy().
			Foreground(mr.theme.Colors.Primary).
			Bold(true)
	case MessageTypeAssistant:
		return mr.theme.Styles.Base.Copy().
			Foreground(mr.theme.Colors.Secondary).
			Bold(true)
	case MessageTypeSystem:
		return mr.theme.Styles.Base.Copy().
			Foreground(mr.theme.Colors.TextMuted).
			Bold(true)
	case MessageTypeError:
		return mr.theme.Styles.Error
	case MessageTypeWarning:
		return mr.theme.Styles.Warning
	case MessageTypeInfo:
		return mr.theme.Styles.Info
	case MessageTypeSuccess:
		return mr.theme.Styles.Success
	case MessageTypeTool:
		return mr.theme.Styles.Base.Copy().
			Foreground(mr.theme.Colors.Accent).
			Bold(true)
	case MessageTypeCode:
		return mr.theme.Styles.Code
	default:
		return mr.theme.Styles.Base
	}
}

// getContentStyle returns the style for message content
func (mr *MessageRenderer) getContentStyle(msgType MessageType) lipgloss.Style {
	baseStyle := mr.theme.Styles.Body.Copy().
		Width(mr.width - 4).
		Padding(1)

	switch msgType {
	case MessageTypeUser:
		return baseStyle.
			Foreground(mr.theme.Colors.Text).
			Background(mr.theme.Colors.Surface).
			Border(lipgloss.RoundedBorder()).
			BorderForeground(mr.theme.Colors.Primary).
			MarginLeft(2)
	case MessageTypeAssistant:
		return baseStyle.
			Foreground(mr.theme.Colors.Text).
			Background(mr.theme.Colors.Background)
	case MessageTypeError:
		return baseStyle.
			Foreground(mr.theme.Colors.Error).
			Background(mr.theme.Colors.Surface).
			Border(lipgloss.RoundedBorder()).
			BorderForeground(mr.theme.Colors.Error)
	case MessageTypeWarning:
		return baseStyle.
			Foreground(mr.theme.Colors.Warning).
			Background(mr.theme.Colors.Surface).
			Border(lipgloss.RoundedBorder()).
			BorderForeground(mr.theme.Colors.Warning)
	case MessageTypeInfo:
		return baseStyle.
			Foreground(mr.theme.Colors.Info).
			Background(mr.theme.Colors.Surface).
			Border(lipgloss.RoundedBorder()).
			BorderForeground(mr.theme.Colors.Info)
	case MessageTypeSuccess:
		return baseStyle.
			Foreground(mr.theme.Colors.Success).
			Background(mr.theme.Colors.Surface).
			Border(lipgloss.RoundedBorder()).
			BorderForeground(mr.theme.Colors.Success)
	default:
		return baseStyle
	}
}

// getStatusStyle returns the style for tool status
func (mr *MessageRenderer) getStatusStyle(status string) lipgloss.Style {
	switch strings.ToLower(status) {
	case "success", "completed", "done":
		return mr.theme.Styles.Success
	case "error", "failed":
		return mr.theme.Styles.Error
	case "warning":
		return mr.theme.Styles.Warning
	case "running", "executing":
		return mr.theme.Styles.Info
	default:
		return mr.theme.Styles.Base.Copy().
			Foreground(mr.theme.Colors.TextMuted)
	}
}

// wrapMessage wraps the entire message in a container
func (mr *MessageRenderer) wrapMessage(msg Message, content string) string {
	containerStyle := mr.theme.Styles.Base.Copy().
		Width(mr.width).
		MarginBottom(1)

	// Add special styling for different message types
	switch msg.Type {
	case MessageTypeError:
		containerStyle = containerStyle.
			Border(lipgloss.RoundedBorder()).
			BorderForeground(mr.theme.Colors.Error).
			Padding(1)
	case MessageTypeWarning:
		containerStyle = containerStyle.
			Border(lipgloss.RoundedBorder()).
			BorderForeground(mr.theme.Colors.Warning).
			Padding(1)
	case MessageTypeSystem:
		containerStyle = containerStyle.
			Background(mr.theme.Colors.Surface).
			Padding(1)
	}

	return containerStyle.Render(content)
}

// wrapText wraps text to fit within the specified width
func (mr *MessageRenderer) wrapText(text string, width int) string {
	if width <= 0 {
		return text
	}

	words := strings.Fields(text)
	if len(words) == 0 {
		return text
	}

	var lines []string
	var currentLine strings.Builder

	for _, word := range words {
		// Check if adding this word would exceed the width
		if currentLine.Len() > 0 && currentLine.Len()+len(word)+1 > width {
			lines = append(lines, currentLine.String())
			currentLine.Reset()
		}

		if currentLine.Len() > 0 {
			currentLine.WriteString(" ")
		}
		currentLine.WriteString(word)
	}

	if currentLine.Len() > 0 {
		lines = append(lines, currentLine.String())
	}

	return strings.Join(lines, "\n")
}

// formatArguments formats tool arguments for display
func (mr *MessageRenderer) formatArguments(args map[string]interface{}) string {
	if len(args) == 0 {
		return "none"
	}

	var parts []string
	for key, value := range args {
		parts = append(parts, fmt.Sprintf("%s=%v", key, value))
	}

	return strings.Join(parts, ", ")
}

// renderProgressBar renders a progress bar
func (mr *MessageRenderer) renderProgressBar(progress float64) string {
	if progress < 0 {
		progress = 0
	}
	if progress > 1 {
		progress = 1
	}

	width := 20
	filled := int(progress * float64(width))
	empty := width - filled

	progressStyle := mr.theme.Styles.Progress.Copy().
		Background(mr.theme.Colors.Primary)

	emptyStyle := mr.theme.Styles.Base.Copy().
		Background(mr.theme.Colors.Surface)

	bar := progressStyle.Render(strings.Repeat("█", filled)) +
		emptyStyle.Render(strings.Repeat("░", empty))

	return fmt.Sprintf("[%s] %.1f%%", bar, progress*100)
}
