/**
 * @license
 * Copyright 2025 Arien LLC
 * License: MIT
 */

package components

import (
	"strings"

	"arien/internal/ui/themes"

	"github.com/charmbracelet/lipgloss"
)

// LayoutType represents different layout types
type LayoutType int

const (
	LayoutTypeVertical LayoutType = iota
	LayoutTypeHorizontal
	LayoutTypeGrid
	LayoutTypeBorder
	LayoutTypeTabs
)

// LayoutComponent represents a layout container
type LayoutComponent struct {
	layoutType LayoutType
	theme      themes.Theme
	width      int
	height     int
	padding    int
	margin     int
	border     bool
	title      string
	
	// Children
	children []LayoutChild
	
	// Grid specific
	columns int
	rows    int
	
	// Border layout specific
	header  *LayoutChild
	footer  *LayoutChild
	left    *LayoutChild
	right   *LayoutChild
	center  *LayoutChild
	
	// Tabs specific
	tabs        []Tab
	activeTab   int
	tabPosition TabPosition
}

// LayoutChild represents a child component in the layout
type LayoutChild struct {
	Content    string
	Weight     float64 // For flexible sizing
	MinSize    int
	MaxSize    int
	Alignment  lipgloss.Position
	Padding    int
	Margin     int
	Border     bool
	Style      lipgloss.Style
}

// Tab represents a tab in a tabbed layout
type Tab struct {
	Title   string
	Content string
	Icon    string
	Enabled bool
	Badge   string
}

// TabPosition represents tab position
type TabPosition int

const (
	TabPositionTop TabPosition = iota
	TabPositionBottom
	TabPositionLeft
	TabPositionRight
)

// NewLayoutComponent creates a new layout component
func NewLayoutComponent(layoutType LayoutType, theme themes.Theme) *LayoutComponent {
	return &LayoutComponent{
		layoutType:  layoutType,
		theme:       theme,
		width:       80,
		height:      24,
		padding:     1,
		margin:      0,
		border:      true,
		columns:     2,
		rows:        2,
		tabPosition: TabPositionTop,
	}
}

// SetSize sets the layout size
func (lc *LayoutComponent) SetSize(width, height int) *LayoutComponent {
	lc.width = width
	lc.height = height
	return lc
}

// SetPadding sets the layout padding
func (lc *LayoutComponent) SetPadding(padding int) *LayoutComponent {
	lc.padding = padding
	return lc
}

// SetMargin sets the layout margin
func (lc *LayoutComponent) SetMargin(margin int) *LayoutComponent {
	lc.margin = margin
	return lc
}

// SetBorder sets whether to show border
func (lc *LayoutComponent) SetBorder(border bool) *LayoutComponent {
	lc.border = border
	return lc
}

// SetTitle sets the layout title
func (lc *LayoutComponent) SetTitle(title string) *LayoutComponent {
	lc.title = title
	return lc
}

// SetGridSize sets the grid dimensions
func (lc *LayoutComponent) SetGridSize(columns, rows int) *LayoutComponent {
	lc.columns = columns
	lc.rows = rows
	return lc
}

// AddChild adds a child to the layout
func (lc *LayoutComponent) AddChild(content string, weight float64) *LayoutComponent {
	child := LayoutChild{
		Content:   content,
		Weight:    weight,
		Alignment: lipgloss.Left,
		Style:     lc.theme.Styles.Base,
	}
	lc.children = append(lc.children, child)
	return lc
}

// AddStyledChild adds a styled child to the layout
func (lc *LayoutComponent) AddStyledChild(child LayoutChild) *LayoutComponent {
	lc.children = append(lc.children, child)
	return lc
}

// SetHeader sets the header for border layout
func (lc *LayoutComponent) SetHeader(content string) *LayoutComponent {
	lc.header = &LayoutChild{
		Content: content,
		Style:   lc.theme.Styles.Header,
	}
	return lc
}

// SetFooter sets the footer for border layout
func (lc *LayoutComponent) SetFooter(content string) *LayoutComponent {
	lc.footer = &LayoutChild{
		Content: content,
		Style:   lc.theme.Styles.Footer,
	}
	return lc
}

// SetLeft sets the left panel for border layout
func (lc *LayoutComponent) SetLeft(content string) *LayoutComponent {
	lc.left = &LayoutChild{
		Content: content,
		Style:   lc.theme.Styles.Sidebar,
	}
	return lc
}

// SetRight sets the right panel for border layout
func (lc *LayoutComponent) SetRight(content string) *LayoutComponent {
	lc.right = &LayoutChild{
		Content: content,
		Style:   lc.theme.Styles.Sidebar,
	}
	return lc
}

// SetCenter sets the center content for border layout
func (lc *LayoutComponent) SetCenter(content string) *LayoutComponent {
	lc.center = &LayoutChild{
		Content: content,
		Style:   lc.theme.Styles.Content,
	}
	return lc
}

// AddTab adds a tab to the layout
func (lc *LayoutComponent) AddTab(title, content, icon string) *LayoutComponent {
	tab := Tab{
		Title:   title,
		Content: content,
		Icon:    icon,
		Enabled: true,
	}
	lc.tabs = append(lc.tabs, tab)
	return lc
}

// SetActiveTab sets the active tab
func (lc *LayoutComponent) SetActiveTab(index int) *LayoutComponent {
	if index >= 0 && index < len(lc.tabs) {
		lc.activeTab = index
	}
	return lc
}

// SetTabPosition sets the tab position
func (lc *LayoutComponent) SetTabPosition(position TabPosition) *LayoutComponent {
	lc.tabPosition = position
	return lc
}

// View renders the layout
func (lc *LayoutComponent) View() string {
	switch lc.layoutType {
	case LayoutTypeVertical:
		return lc.renderVertical()
	case LayoutTypeHorizontal:
		return lc.renderHorizontal()
	case LayoutTypeGrid:
		return lc.renderGrid()
	case LayoutTypeBorder:
		return lc.renderBorder()
	case LayoutTypeTabs:
		return lc.renderTabs()
	default:
		return lc.renderVertical()
	}
}

// renderVertical renders a vertical layout
func (lc *LayoutComponent) renderVertical() string {
	if len(lc.children) == 0 {
		return ""
	}

	var parts []string
	availableHeight := lc.height - (lc.padding * 2)
	if lc.border {
		availableHeight -= 2
	}

	// Calculate heights based on weights
	totalWeight := 0.0
	for _, child := range lc.children {
		totalWeight += child.Weight
	}

	for _, child := range lc.children {
		childHeight := int(float64(availableHeight) * (child.Weight / totalWeight))
		if childHeight < 1 {
			childHeight = 1
		}

		style := child.Style.Copy().
			Width(lc.width - (lc.padding * 2) - 2).
			Height(childHeight)

		if child.Border {
			style = style.Border(lipgloss.RoundedBorder())
		}

		parts = append(parts, style.Render(child.Content))
	}

	content := lipgloss.JoinVertical(lipgloss.Left, parts...)
	return lc.wrapContent(content)
}

// renderHorizontal renders a horizontal layout
func (lc *LayoutComponent) renderHorizontal() string {
	if len(lc.children) == 0 {
		return ""
	}

	var parts []string
	availableWidth := lc.width - (lc.padding * 2)
	if lc.border {
		availableWidth -= 2
	}

	// Calculate widths based on weights
	totalWeight := 0.0
	for _, child := range lc.children {
		totalWeight += child.Weight
	}

	for _, child := range lc.children {
		childWidth := int(float64(availableWidth) * (child.Weight / totalWeight))
		if childWidth < 1 {
			childWidth = 1
		}

		style := child.Style.Copy().
			Width(childWidth).
			Height(lc.height - (lc.padding * 2) - 2)

		if child.Border {
			style = style.Border(lipgloss.RoundedBorder())
		}

		parts = append(parts, style.Render(child.Content))
	}

	content := lipgloss.JoinHorizontal(lipgloss.Top, parts...)
	return lc.wrapContent(content)
}

// renderGrid renders a grid layout
func (lc *LayoutComponent) renderGrid() string {
	if len(lc.children) == 0 {
		return ""
	}

	cellWidth := (lc.width - (lc.padding * 2) - 2) / lc.columns
	cellHeight := (lc.height - (lc.padding * 2) - 2) / lc.rows

	var rows []string
	childIndex := 0

	for row := 0; row < lc.rows; row++ {
		var cells []string

		for col := 0; col < lc.columns; col++ {
			var cellContent string
			if childIndex < len(lc.children) {
				child := lc.children[childIndex]
				style := child.Style.Copy().
					Width(cellWidth).
					Height(cellHeight)

				if child.Border {
					style = style.Border(lipgloss.RoundedBorder())
				}

				cellContent = style.Render(child.Content)
				childIndex++
			} else {
				// Empty cell
				style := lc.theme.Styles.Base.Copy().
					Width(cellWidth).
					Height(cellHeight)
				cellContent = style.Render("")
			}

			cells = append(cells, cellContent)
		}

		rows = append(rows, lipgloss.JoinHorizontal(lipgloss.Top, cells...))
	}

	content := lipgloss.JoinVertical(lipgloss.Left, rows...)
	return lc.wrapContent(content)
}

// renderBorder renders a border layout
func (lc *LayoutComponent) renderBorder() string {
	var parts []string

	// Calculate dimensions
	headerHeight := 3
	footerHeight := 3
	sidebarWidth := 20

	contentWidth := lc.width - (sidebarWidth * 2) - (lc.padding * 2) - 2
	contentHeight := lc.height - headerHeight - footerHeight - (lc.padding * 2) - 2

	// Header
	if lc.header != nil {
		headerStyle := lc.header.Style.Copy().
			Width(lc.width - (lc.padding * 2) - 2).
			Height(headerHeight)
		parts = append(parts, headerStyle.Render(lc.header.Content))
	}

	// Middle section (left, center, right)
	var middleParts []string

	// Left sidebar
	if lc.left != nil {
		leftStyle := lc.left.Style.Copy().
			Width(sidebarWidth).
			Height(contentHeight)
		middleParts = append(middleParts, leftStyle.Render(lc.left.Content))
	}

	// Center content
	if lc.center != nil {
		centerStyle := lc.center.Style.Copy().
			Width(contentWidth).
			Height(contentHeight)
		middleParts = append(middleParts, centerStyle.Render(lc.center.Content))
	}

	// Right sidebar
	if lc.right != nil {
		rightStyle := lc.right.Style.Copy().
			Width(sidebarWidth).
			Height(contentHeight)
		middleParts = append(middleParts, rightStyle.Render(lc.right.Content))
	}

	if len(middleParts) > 0 {
		middle := lipgloss.JoinHorizontal(lipgloss.Top, middleParts...)
		parts = append(parts, middle)
	}

	// Footer
	if lc.footer != nil {
		footerStyle := lc.footer.Style.Copy().
			Width(lc.width - (lc.padding * 2) - 2).
			Height(footerHeight)
		parts = append(parts, footerStyle.Render(lc.footer.Content))
	}

	content := lipgloss.JoinVertical(lipgloss.Left, parts...)
	return lc.wrapContent(content)
}

// renderTabs renders a tabbed layout
func (lc *LayoutComponent) renderTabs() string {
	if len(lc.tabs) == 0 {
		return ""
	}

	var result strings.Builder

	// Render tab headers
	tabHeaders := lc.renderTabHeaders()

	// Render active tab content
	var tabContent string
	if lc.activeTab < len(lc.tabs) {
		contentStyle := lc.theme.Styles.Content.Copy().
			Width(lc.width - (lc.padding * 2) - 2).
			Height(lc.height - 5 - (lc.padding * 2)) // Account for tab header height

		tabContent = contentStyle.Render(lc.tabs[lc.activeTab].Content)
	}

	// Combine based on tab position
	switch lc.tabPosition {
	case TabPositionTop:
		result.WriteString(tabHeaders)
		result.WriteString("\n")
		result.WriteString(tabContent)
	case TabPositionBottom:
		result.WriteString(tabContent)
		result.WriteString("\n")
		result.WriteString(tabHeaders)
	case TabPositionLeft:
		content := lipgloss.JoinHorizontal(lipgloss.Top, tabHeaders, tabContent)
		result.WriteString(content)
	case TabPositionRight:
		content := lipgloss.JoinHorizontal(lipgloss.Top, tabContent, tabHeaders)
		result.WriteString(content)
	}

	return lc.wrapContent(result.String())
}

// renderTabHeaders renders the tab headers
func (lc *LayoutComponent) renderTabHeaders() string {
	var tabParts []string

	for i, tab := range lc.tabs {
		var tabStyle lipgloss.Style

		if i == lc.activeTab {
			tabStyle = lc.theme.Styles.ButtonActive.Copy()
		} else {
			tabStyle = lc.theme.Styles.Button.Copy()
		}

		if !tab.Enabled {
			tabStyle = tabStyle.
				Foreground(lc.theme.Colors.TextMuted).
				Background(lc.theme.Colors.Surface)
		}

		// Build tab content
		var tabContent strings.Builder
		if tab.Icon != "" {
			tabContent.WriteString(tab.Icon)
			tabContent.WriteString(" ")
		}
		tabContent.WriteString(tab.Title)
		if tab.Badge != "" {
			tabContent.WriteString(" ")
			badgeStyle := lc.theme.Styles.Base.Copy().
				Foreground(lc.theme.Colors.Warning).
				Background(lc.theme.Colors.Surface).
				Padding(0, 1)
			tabContent.WriteString(badgeStyle.Render(tab.Badge))
		}

		tabParts = append(tabParts, tabStyle.Render(tabContent.String()))
	}

	if lc.tabPosition == TabPositionLeft || lc.tabPosition == TabPositionRight {
		return lipgloss.JoinVertical(lipgloss.Left, tabParts...)
	}

	return lipgloss.JoinHorizontal(lipgloss.Top, tabParts...)
}

// wrapContent wraps the content with border, padding, and title
func (lc *LayoutComponent) wrapContent(content string) string {
	style := lc.theme.Styles.Base.Copy().
		Width(lc.width).
		Height(lc.height)

	if lc.padding > 0 {
		style = style.Padding(lc.padding)
	}

	if lc.margin > 0 {
		style = style.Margin(lc.margin)
	}

	if lc.border {
		style = style.
			Border(lipgloss.RoundedBorder()).
			BorderForeground(lc.theme.Colors.Border)
	}

	// Add title if present
	if lc.title != "" {
		titleStyle := lc.theme.Styles.Title.Copy().
			Foreground(lc.theme.Colors.Primary).
			Background(lc.theme.Colors.Background).
			Padding(0, 1)

		titleContent := titleStyle.Render(lc.title)

		// Position title at top of border
		if lc.border {
			content = titleContent + "\n" + content
		} else {
			content = titleContent + "\n\n" + content
		}
	}

	return style.Render(content)
}

// GetActiveTabContent returns the content of the active tab
func (lc *LayoutComponent) GetActiveTabContent() string {
	if lc.activeTab < len(lc.tabs) {
		return lc.tabs[lc.activeTab].Content
	}
	return ""
}

// GetTabCount returns the number of tabs
func (lc *LayoutComponent) GetTabCount() int {
	return len(lc.tabs)
}

// IsTabEnabled returns whether a tab is enabled
func (lc *LayoutComponent) IsTabEnabled(index int) bool {
	if index >= 0 && index < len(lc.tabs) {
		return lc.tabs[index].Enabled
	}
	return false
}

// SetTabEnabled sets whether a tab is enabled
func (lc *LayoutComponent) SetTabEnabled(index int, enabled bool) *LayoutComponent {
	if index >= 0 && index < len(lc.tabs) {
		lc.tabs[index].Enabled = enabled
	}
	return lc
}

// SetTabBadge sets a badge for a tab
func (lc *LayoutComponent) SetTabBadge(index int, badge string) *LayoutComponent {
	if index >= 0 && index < len(lc.tabs) {
		lc.tabs[index].Badge = badge
	}
	return lc
}
