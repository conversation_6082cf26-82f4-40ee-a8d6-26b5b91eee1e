/**
 * @license
 * Copyright 2025 Arien LLC
 * License: MIT
 */

package components

import (
	"strings"
	"unicode"

	"arien/internal/ui/themes"

	tea "github.com/charmbracelet/bubbletea"
	"github.com/charmbracelet/lipgloss"
)

// InputType represents different types of input fields
type InputType int

const (
	InputTypeText InputType = iota
	InputTypePassword
	InputTypeMultiline
	InputTypeNumber
	InputTypeEmail
	InputTypeURL
)

// InputComponent represents an enhanced input component
type InputComponent struct {
	// Core properties
	value       string
	placeholder string
	inputType   InputType
	theme       themes.Theme
	
	// State
	focused     bool
	cursor      int
	selection   [2]int // start, end
	
	// Appearance
	width       int
	height      int
	maxLength   int
	showCursor  bool
	
	// Validation
	validator   func(string) error
	required    bool
	errorMsg    string
	
	// Multiline specific
	lines       []string
	currentLine int
	scrollY     int
	
	// History
	history     []string
	historyPos  int
	
	// Suggestions
	suggestions []string
	showSuggestions bool
	selectedSuggestion int
}

// NewInputComponent creates a new input component
func NewInputComponent(inputType InputType, theme themes.Theme) *InputComponent {
	return &InputComponent{
		inputType:   inputType,
		theme:       theme,
		width:       40,
		height:      1,
		maxLength:   -1,
		showCursor:  true,
		selection:   [2]int{-1, -1},
		historyPos:  -1,
	}
}

// SetPlaceholder sets the placeholder text
func (ic *InputComponent) SetPlaceholder(placeholder string) *InputComponent {
	ic.placeholder = placeholder
	return ic
}

// SetSize sets the component size
func (ic *InputComponent) SetSize(width, height int) *InputComponent {
	ic.width = width
	ic.height = height
	if ic.inputType == InputTypeMultiline {
		ic.updateLines()
	}
	return ic
}

// SetMaxLength sets the maximum input length
func (ic *InputComponent) SetMaxLength(maxLength int) *InputComponent {
	ic.maxLength = maxLength
	return ic
}

// SetValidator sets the validation function
func (ic *InputComponent) SetValidator(validator func(string) error) *InputComponent {
	ic.validator = validator
	return ic
}

// SetRequired sets whether the input is required
func (ic *InputComponent) SetRequired(required bool) *InputComponent {
	ic.required = required
	return ic
}

// SetTheme updates the theme
func (ic *InputComponent) SetTheme(theme themes.Theme) {
	ic.theme = theme
}

// SetValue sets the input value
func (ic *InputComponent) SetValue(value string) {
	ic.value = value
	ic.cursor = len(value)
	if ic.inputType == InputTypeMultiline {
		ic.updateLines()
	}
	ic.validate()
}

// GetValue returns the current input value
func (ic *InputComponent) GetValue() string {
	return ic.value
}

// SetFocus sets the focus state
func (ic *InputComponent) SetFocus(focused bool) {
	ic.focused = focused
	if focused {
		ic.showCursor = true
	}
}

// IsFocused returns whether the component is focused
func (ic *InputComponent) IsFocused() bool {
	return ic.focused
}

// IsValid returns whether the current value is valid
func (ic *InputComponent) IsValid() bool {
	return ic.errorMsg == ""
}

// GetError returns the current error message
func (ic *InputComponent) GetError() string {
	return ic.errorMsg
}

// AddToHistory adds a value to the input history
func (ic *InputComponent) AddToHistory(value string) {
	if value == "" {
		return
	}
	
	// Remove if already exists
	for i, h := range ic.history {
		if h == value {
			ic.history = append(ic.history[:i], ic.history[i+1:]...)
			break
		}
	}
	
	// Add to beginning
	ic.history = append([]string{value}, ic.history...)
	
	// Limit history size
	if len(ic.history) > 50 {
		ic.history = ic.history[:50]
	}
	
	ic.historyPos = -1
}

// SetSuggestions sets the suggestion list
func (ic *InputComponent) SetSuggestions(suggestions []string) {
	ic.suggestions = suggestions
	ic.selectedSuggestion = 0
}

// Update handles tea.Msg updates
func (ic *InputComponent) Update(msg tea.Msg) (*InputComponent, tea.Cmd) {
	if !ic.focused {
		return ic, nil
	}

	switch msg := msg.(type) {
	case tea.KeyMsg:
		return ic.handleKeyMsg(msg)
	}

	return ic, nil
}

// handleKeyMsg handles keyboard input
func (ic *InputComponent) handleKeyMsg(msg tea.KeyMsg) (*InputComponent, tea.Cmd) {
	switch msg.String() {
	case "ctrl+c":
		return ic, tea.Quit
		
	case "enter":
		if ic.inputType == InputTypeMultiline {
			return ic.insertChar('\n'), nil
		}
		// For single-line inputs, enter typically submits
		return ic, nil
		
	case "backspace":
		return ic.deleteChar(), nil
		
	case "delete":
		return ic.deleteCharForward(), nil
		
	case "left", "ctrl+b":
		return ic.moveCursor(-1), nil
		
	case "right", "ctrl+f":
		return ic.moveCursor(1), nil
		
	case "up":
		if ic.inputType == InputTypeMultiline {
			return ic.moveLineUp(), nil
		}
		return ic.historyUp(), nil
		
	case "down":
		if ic.inputType == InputTypeMultiline {
			return ic.moveLineDown(), nil
		}
		return ic.historyDown(), nil
		
	case "home", "ctrl+a":
		return ic.moveToStart(), nil
		
	case "end", "ctrl+e":
		return ic.moveToEnd(), nil
		
	case "ctrl+w":
		return ic.deleteWord(), nil
		
	case "ctrl+u":
		return ic.deleteToStart(), nil
		
	case "ctrl+k":
		return ic.deleteToEnd(), nil
		
	case "tab":
		if ic.showSuggestions && len(ic.suggestions) > 0 {
			return ic.applySuggestion(), nil
		}
		return ic.insertChar('\t'), nil
		
	default:
		// Handle regular character input
		if len(msg.String()) == 1 {
			char := rune(msg.String()[0])
			if unicode.IsPrint(char) || char == '\t' {
				return ic.insertChar(char), nil
			}
		}
	}

	return ic, nil
}

// insertChar inserts a character at the cursor position
func (ic *InputComponent) insertChar(char rune) *InputComponent {
	if ic.maxLength >= 0 && len(ic.value) >= ic.maxLength {
		return ic
	}

	// Handle password masking
	if ic.inputType == InputTypePassword && char != '\n' && char != '\t' {
		// Store actual character but don't display it
	}

	// Insert character
	before := ic.value[:ic.cursor]
	after := ic.value[ic.cursor:]
	ic.value = before + string(char) + after
	ic.cursor++

	if ic.inputType == InputTypeMultiline {
		ic.updateLines()
	}

	ic.validate()
	ic.updateSuggestions()
	
	return ic
}

// deleteChar deletes the character before the cursor
func (ic *InputComponent) deleteChar() *InputComponent {
	if ic.cursor > 0 {
		before := ic.value[:ic.cursor-1]
		after := ic.value[ic.cursor:]
		ic.value = before + after
		ic.cursor--
		
		if ic.inputType == InputTypeMultiline {
			ic.updateLines()
		}
		
		ic.validate()
		ic.updateSuggestions()
	}
	return ic
}

// deleteCharForward deletes the character after the cursor
func (ic *InputComponent) deleteCharForward() *InputComponent {
	if ic.cursor < len(ic.value) {
		before := ic.value[:ic.cursor]
		after := ic.value[ic.cursor+1:]
		ic.value = before + after
		
		if ic.inputType == InputTypeMultiline {
			ic.updateLines()
		}
		
		ic.validate()
		ic.updateSuggestions()
	}
	return ic
}

// moveCursor moves the cursor by the specified offset
func (ic *InputComponent) moveCursor(offset int) *InputComponent {
	newPos := ic.cursor + offset
	if newPos < 0 {
		newPos = 0
	}
	if newPos > len(ic.value) {
		newPos = len(ic.value)
	}
	ic.cursor = newPos
	return ic
}

// moveToStart moves cursor to the beginning
func (ic *InputComponent) moveToStart() *InputComponent {
	ic.cursor = 0
	return ic
}

// moveToEnd moves cursor to the end
func (ic *InputComponent) moveToEnd() *InputComponent {
	ic.cursor = len(ic.value)
	return ic
}

// deleteWord deletes the word before the cursor
func (ic *InputComponent) deleteWord() *InputComponent {
	if ic.cursor == 0 {
		return ic
	}

	// Find the start of the current word
	start := ic.cursor - 1
	for start > 0 && !unicode.IsSpace(rune(ic.value[start-1])) {
		start--
	}

	before := ic.value[:start]
	after := ic.value[ic.cursor:]
	ic.value = before + after
	ic.cursor = start

	if ic.inputType == InputTypeMultiline {
		ic.updateLines()
	}

	ic.validate()
	ic.updateSuggestions()
	return ic
}

// deleteToStart deletes from cursor to start
func (ic *InputComponent) deleteToStart() *InputComponent {
	ic.value = ic.value[ic.cursor:]
	ic.cursor = 0
	
	if ic.inputType == InputTypeMultiline {
		ic.updateLines()
	}
	
	ic.validate()
	ic.updateSuggestions()
	return ic
}

// deleteToEnd deletes from cursor to end
func (ic *InputComponent) deleteToEnd() *InputComponent {
	ic.value = ic.value[:ic.cursor]

	if ic.inputType == InputTypeMultiline {
		ic.updateLines()
	}

	ic.validate()
	ic.updateSuggestions()
	return ic
}

// moveLineUp moves cursor up one line (multiline only)
func (ic *InputComponent) moveLineUp() *InputComponent {
	if ic.inputType != InputTypeMultiline || ic.currentLine == 0 {
		return ic
	}

	ic.currentLine--
	// Try to maintain horizontal position
	if ic.currentLine < len(ic.lines) {
		lineStart := ic.getLineStart(ic.currentLine)
		maxPos := lineStart + len(ic.lines[ic.currentLine])
		desiredPos := lineStart + (ic.cursor - ic.getLineStart(ic.currentLine+1))
		if desiredPos > maxPos {
			ic.cursor = maxPos
		} else {
			ic.cursor = desiredPos
		}
	}
	return ic
}

// moveLineDown moves cursor down one line (multiline only)
func (ic *InputComponent) moveLineDown() *InputComponent {
	if ic.inputType != InputTypeMultiline || ic.currentLine >= len(ic.lines)-1 {
		return ic
	}

	ic.currentLine++
	// Try to maintain horizontal position
	if ic.currentLine < len(ic.lines) {
		lineStart := ic.getLineStart(ic.currentLine)
		maxPos := lineStart + len(ic.lines[ic.currentLine])
		desiredPos := lineStart + (ic.cursor - ic.getLineStart(ic.currentLine-1))
		if desiredPos > maxPos {
			ic.cursor = maxPos
		} else {
			ic.cursor = desiredPos
		}
	}
	return ic
}

// historyUp moves up in input history
func (ic *InputComponent) historyUp() *InputComponent {
	if len(ic.history) == 0 {
		return ic
	}

	if ic.historyPos == -1 {
		ic.historyPos = 0
	} else if ic.historyPos < len(ic.history)-1 {
		ic.historyPos++
	}

	if ic.historyPos < len(ic.history) {
		ic.SetValue(ic.history[ic.historyPos])
	}

	return ic
}

// historyDown moves down in input history
func (ic *InputComponent) historyDown() *InputComponent {
	if len(ic.history) == 0 || ic.historyPos == -1 {
		return ic
	}

	if ic.historyPos > 0 {
		ic.historyPos--
		ic.SetValue(ic.history[ic.historyPos])
	} else {
		ic.historyPos = -1
		ic.SetValue("")
	}

	return ic
}

// applySuggestion applies the selected suggestion
func (ic *InputComponent) applySuggestion() *InputComponent {
	if ic.selectedSuggestion < len(ic.suggestions) {
		ic.SetValue(ic.suggestions[ic.selectedSuggestion])
		ic.showSuggestions = false
	}
	return ic
}

// updateLines updates the lines array for multiline input
func (ic *InputComponent) updateLines() {
	if ic.inputType != InputTypeMultiline {
		return
	}

	ic.lines = strings.Split(ic.value, "\n")

	// Update current line based on cursor position
	pos := 0
	for i, line := range ic.lines {
		if pos+len(line) >= ic.cursor {
			ic.currentLine = i
			break
		}
		pos += len(line) + 1 // +1 for newline
	}
}

// getLineStart returns the start position of a line
func (ic *InputComponent) getLineStart(lineNum int) int {
	if lineNum < 0 || lineNum >= len(ic.lines) {
		return 0
	}

	pos := 0
	for i := 0; i < lineNum; i++ {
		pos += len(ic.lines[i]) + 1 // +1 for newline
	}
	return pos
}

// validate validates the current input value
func (ic *InputComponent) validate() {
	ic.errorMsg = ""

	// Check required
	if ic.required && strings.TrimSpace(ic.value) == "" {
		ic.errorMsg = "This field is required"
		return
	}

	// Check custom validator
	if ic.validator != nil {
		if err := ic.validator(ic.value); err != nil {
			ic.errorMsg = err.Error()
			return
		}
	}

	// Type-specific validation
	switch ic.inputType {
	case InputTypeEmail:
		if ic.value != "" && !ic.isValidEmail(ic.value) {
			ic.errorMsg = "Invalid email format"
		}
	case InputTypeURL:
		if ic.value != "" && !ic.isValidURL(ic.value) {
			ic.errorMsg = "Invalid URL format"
		}
	case InputTypeNumber:
		if ic.value != "" && !ic.isValidNumber(ic.value) {
			ic.errorMsg = "Invalid number format"
		}
	}
}

// updateSuggestions updates the suggestion list based on current input
func (ic *InputComponent) updateSuggestions() {
	if len(ic.suggestions) == 0 {
		ic.showSuggestions = false
		return
	}

	// Filter suggestions based on current input
	if ic.value == "" {
		ic.showSuggestions = true
		return
	}

	var filtered []string
	for _, suggestion := range ic.suggestions {
		if strings.HasPrefix(strings.ToLower(suggestion), strings.ToLower(ic.value)) {
			filtered = append(filtered, suggestion)
		}
	}

	ic.showSuggestions = len(filtered) > 0
	if ic.showSuggestions {
		ic.suggestions = filtered
		ic.selectedSuggestion = 0
	}
}

// Helper validation methods
func (ic *InputComponent) isValidEmail(email string) bool {
	// Simple email validation
	return strings.Contains(email, "@") && strings.Contains(email, ".")
}

func (ic *InputComponent) isValidURL(url string) bool {
	// Simple URL validation
	return strings.HasPrefix(url, "http://") || strings.HasPrefix(url, "https://")
}

func (ic *InputComponent) isValidNumber(num string) bool {
	// Simple number validation
	for _, char := range num {
		if !unicode.IsDigit(char) && char != '.' && char != '-' {
			return false
		}
	}
	return true
}

// View renders the input component
func (ic *InputComponent) View() string {
	var result strings.Builder

	// Input field
	inputContent := ic.renderInput()
	result.WriteString(inputContent)

	// Error message
	if ic.errorMsg != "" {
		result.WriteString("\n")
		errorStyle := ic.theme.Styles.Error.Copy().
			MarginTop(1)
		result.WriteString(errorStyle.Render("⚠ " + ic.errorMsg))
	}

	// Suggestions
	if ic.showSuggestions && len(ic.suggestions) > 0 {
		result.WriteString("\n")
		suggestions := ic.renderSuggestions()
		result.WriteString(suggestions)
	}

	return result.String()
}

// renderInput renders the main input field
func (ic *InputComponent) renderInput() string {
	// Determine display value
	displayValue := ic.value
	if ic.inputType == InputTypePassword {
		displayValue = strings.Repeat("*", len(ic.value))
	}

	// Add cursor if focused
	if ic.focused && ic.showCursor {
		if ic.cursor <= len(displayValue) {
			before := displayValue[:ic.cursor]
			after := displayValue[ic.cursor:]
			displayValue = before + "█" + after
		}
	}

	// Handle placeholder
	if displayValue == "" && ic.placeholder != "" {
		placeholderStyle := ic.theme.Styles.Base.Copy().
			Foreground(ic.theme.Colors.TextMuted).
			Italic(true)
		displayValue = placeholderStyle.Render(ic.placeholder)
	}

	// Create input style
	inputStyle := ic.getInputStyle()

	// Handle multiline
	if ic.inputType == InputTypeMultiline {
		return ic.renderMultilineInput(displayValue, inputStyle)
	}

	return inputStyle.Render(displayValue)
}

// renderMultilineInput renders a multiline input field
func (ic *InputComponent) renderMultilineInput(displayValue string, style lipgloss.Style) string {
	lines := strings.Split(displayValue, "\n")

	// Ensure we have enough lines to fill the height
	for len(lines) < ic.height {
		lines = append(lines, "")
	}

	// Handle scrolling if content exceeds height
	startLine := ic.scrollY
	endLine := startLine + ic.height
	if endLine > len(lines) {
		endLine = len(lines)
	}

	visibleLines := lines[startLine:endLine]
	content := strings.Join(visibleLines, "\n")

	return style.Height(ic.height).Render(content)
}

// renderSuggestions renders the suggestion dropdown
func (ic *InputComponent) renderSuggestions() string {
	if len(ic.suggestions) == 0 {
		return ""
	}

	var result strings.Builder

	suggestionStyle := ic.theme.Styles.Base.Copy().
		Background(ic.theme.Colors.Surface).
		Border(lipgloss.RoundedBorder()).
		BorderForeground(ic.theme.Colors.Border).
		Padding(1).
		Width(ic.width)

	selectedStyle := ic.theme.Styles.Base.Copy().
		Background(ic.theme.Colors.Selection).
		Foreground(ic.theme.Colors.Text).
		Bold(true)

	normalStyle := ic.theme.Styles.Base.Copy().
		Foreground(ic.theme.Colors.TextSecondary)

	for i, suggestion := range ic.suggestions {
		if i > 0 {
			result.WriteString("\n")
		}

		if i == ic.selectedSuggestion {
			result.WriteString(selectedStyle.Render("▶ " + suggestion))
		} else {
			result.WriteString(normalStyle.Render("  " + suggestion))
		}
	}

	return suggestionStyle.Render(result.String())
}

// getInputStyle returns the appropriate style for the input field
func (ic *InputComponent) getInputStyle() lipgloss.Style {
	baseStyle := ic.theme.Styles.Input.Copy().
		Width(ic.width)

	if ic.focused {
		baseStyle = ic.theme.Styles.InputFocus.Copy().
			Width(ic.width)
	}

	// Add error styling if invalid
	if ic.errorMsg != "" {
		baseStyle = baseStyle.
			BorderForeground(ic.theme.Colors.Error)
	}

	return baseStyle
}
